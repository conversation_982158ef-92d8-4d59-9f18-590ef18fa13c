<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研发BOM管理模块修改测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .left-panel {
            width: 240px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        .search-input {
            padding: 10px;
            border-bottom: 1px solid #e4e7ed;
        }
        .search-input input {
            width: 100%;
            padding: 8px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
        }
        .model-filter-buttons {
            padding: 10px;
            border-bottom: 1px solid #e4e7ed;
            display: flex;
            gap: 8px;
        }
        .filter-btn {
            flex: 1;
            font-size: 12px;
            padding: 6px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
        }
        .filter-btn.active {
            background: #409eff;
            color: white;
            border-color: #409eff;
        }
        .filter-btn:hover {
            background: #ecf5ff;
            border-color: #409eff;
        }
        .filter-btn.active:hover {
            background: #337ecc;
        }
        .model-list {
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
        }
        .model-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid transparent;
        }
        .model-item:hover {
            background-color: #f5f7fa;
        }
        .model-item.active {
            background-color: #ecf5ff;
            border-left: 3px solid #409eff;
        }
        .model-info {
            flex: 1;
        }
        .model-name {
            font-weight: 500;
            margin-bottom: 4px;
        }
        .model-code {
            font-size: 12px;
            color: #909399;
        }
        .model-status {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .model-status.enabled {
            background: #f0f9ff;
            color: #67c23a;
        }
        .model-status.disabled {
            background: #fef0f0;
            color: #f56c6c;
        }
        .type-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            margin-left: 5px;
        }
        .type-finished {
            background: #e1f3d8;
            color: #67c23a;
        }
        .type-component {
            background: #fdf6ec;
            color: #e6a23c;
        }
        .summary {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>研发BOM管理模块 - 左侧型号列表修改演示</h1>
        
        <div class="left-panel">
            <div class="search-input">
                <input type="text" placeholder="请输入型号进行搜索" id="searchInput">
            </div>
            
            <!-- 新增的型号类型过滤按钮 -->
            <div class="model-filter-buttons">
                <button class="filter-btn active" data-type="all">全部</button>
                <button class="filter-btn" data-type="finished">成品</button>
                <button class="filter-btn" data-type="component">组件</button>
            </div>
            
            <div class="model-list" id="modelList">
                <!-- 型号列表将通过JavaScript动态生成 -->
            </div>
        </div>
        
        <div class="summary">
            <h3>修改说明：</h3>
            <ul>
                <li>✅ 在搜索框下方添加了三个过滤按钮：全部、成品、组件</li>
                <li>✅ 按钮采用Element Plus风格设计，支持激活状态</li>
                <li>✅ 点击按钮可以过滤不同类型的型号</li>
                <li>✅ 搜索功能与类型过滤可以同时使用</li>
                <li>✅ 型号数据增加了type字段来区分成品和组件</li>
                <li>✅ 界面布局保持原有风格，无缝集成</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟型号数据
        const models = [
            { id: 1, name: '断路器A型', code: 'MC001', status: '启用', type: 'finished' },
            { id: 2, name: '继电器B型', code: 'MC002', status: '启用', type: 'component' },
            { id: 3, name: '接触器C型', code: 'MC003', status: '停用', type: 'component' },
            { id: 4, name: '变压器D型', code: 'MC004', status: '启用', type: 'finished' },
            { id: 5, name: '控制模块E型', code: 'MC005', status: '启用', type: 'component' },
            { id: 6, name: '配电柜F型', code: 'MC006', status: '启用', type: 'finished' },
        ];

        let currentFilter = 'all';
        let searchText = '';

        function renderModelList() {
            const modelList = document.getElementById('modelList');
            let filteredModels = models;

            // 按类型过滤
            if (currentFilter !== 'all') {
                filteredModels = filteredModels.filter(model => model.type === currentFilter);
            }

            // 按搜索文本过滤
            if (searchText) {
                const search = searchText.toLowerCase();
                filteredModels = filteredModels.filter(model => 
                    model.name.toLowerCase().includes(search) || 
                    model.code.toLowerCase().includes(search)
                );
            }

            modelList.innerHTML = filteredModels.map(model => `
                <div class="model-item">
                    <div class="model-info">
                        <div class="model-name">
                            ${model.name}
                            <span class="type-badge type-${model.type}">
                                ${model.type === 'finished' ? '成品' : '组件'}
                            </span>
                        </div>
                        <div class="model-code">${model.code}</div>
                    </div>
                    <div class="model-status ${model.status === '启用' ? 'enabled' : 'disabled'}">
                        ${model.status}
                    </div>
                </div>
            `).join('');
        }

        // 初始化事件监听
        document.addEventListener('DOMContentLoaded', function() {
            // 过滤按钮事件
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的激活状态
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    // 激活当前按钮
                    this.classList.add('active');
                    // 更新过滤器
                    currentFilter = this.dataset.type;
                    renderModelList();
                });
            });

            // 搜索输入事件
            document.getElementById('searchInput').addEventListener('input', function() {
                searchText = this.value;
                renderModelList();
            });

            // 初始渲染
            renderModelList();
        });
    </script>
</body>
</html>
