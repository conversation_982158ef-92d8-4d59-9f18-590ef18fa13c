<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API集成测试 - 研发BOM管理模块</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .api-test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
        }
        .api-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .test-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #337ecc;
        }
        .result-area {
            background: #f8f9fa;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            min-height: 100px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            color: #67c23a;
        }
        .error {
            color: #f56c6c;
        }
        .loading {
            color: #409eff;
        }
        .feature-list {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .feature-list h4 {
            margin-top: 0;
            color: #409eff;
        }
        .feature-list ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API集成测试 - 研发BOM管理模块</h1>
        
        <div class="feature-list">
            <h4>✅ 已完成的功能集成：</h4>
            <ul>
                <li><strong>成品API集成</strong>：点击"成品"按钮时调用 <code>/api/models/products</code></li>
                <li><strong>组件API集成</strong>：点击"组件"按钮时调用 <code>/api/models/components</code>（带回退机制）</li>
                <li><strong>数据格式适配</strong>：API响应数据自动转换为前端Model格式</li>
                <li><strong>加载状态管理</strong>：显示加载动画和状态提示</li>
                <li><strong>错误处理</strong>：网络错误和API错误的友好提示</li>
                <li><strong>搜索功能保持</strong>：API数据加载后仍支持本地搜索过滤</li>
            </ul>
        </div>

        <div class="api-test-section">
            <h3>🔗 成品API测试</h3>
            <div class="api-url">
                GET http://***************:5221/api/models/products?page=1&pageSize=12
            </div>
            <button class="test-button" onclick="testProductsAPI()">测试成品API</button>
            <button class="test-button" onclick="clearResult('products-result')">清除结果</button>
            <div id="products-result" class="result-area">点击"测试成品API"按钮开始测试...</div>
        </div>

        <div class="api-test-section">
            <h3>🔗 组件API测试</h3>
            <div class="api-url">
                GET http://***************:5221/api/models/components?page=1&pageSize=12
            </div>
            <button class="test-button" onclick="testComponentsAPI()">测试组件API</button>
            <button class="test-button" onclick="clearResult('components-result')">清除结果</button>
            <div id="components-result" class="result-area">点击"测试组件API"按钮开始测试...</div>
        </div>

        <div class="api-test-section">
            <h3>📋 API响应格式说明</h3>
            <p>根据提供的API文档，响应格式应该如下：</p>
            <div class="result-area">{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "产品名称",
        "code": "产品编码", 
        "status": true,
        "specification": "规格",
        "description": "描述",
        "createTime": "2023-07-20T15:35:04.3082116",
        "updateTime": "2023-07-20T15:35:04.3082116"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 12
  }
}</div>
        </div>

        <div class="api-test-section">
            <h3>🔧 前端集成说明</h3>
            <div class="feature-list">
                <h4>数据转换逻辑：</h4>
                <ul>
                    <li><strong>API字段映射</strong>：API的字段直接映射到前端Model接口</li>
                    <li><strong>类型标记</strong>：成品API返回的数据自动标记为 type: 'finished'</li>
                    <li><strong>状态转换</strong>：API的boolean status转换为前端显示的"启用/停用"</li>
                    <li><strong>错误回退</strong>：API调用失败时使用本地示例数据</li>
                </ul>
                
                <h4>用户交互流程：</h4>
                <ul>
                    <li>1. 用户点击"成品"按钮</li>
                    <li>2. 显示加载状态</li>
                    <li>3. 调用成品API获取数据</li>
                    <li>4. 转换数据格式并更新列表</li>
                    <li>5. 显示成功消息和数据数量</li>
                    <li>6. 支持搜索过滤API返回的数据</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        async function testProductsAPI() {
            const resultDiv = document.getElementById('products-result');
            resultDiv.innerHTML = '<span class="loading">正在测试成品API...</span>';
            
            try {
                const response = await fetch('http://***************:5221/api/models/products?page=1&pageSize=12', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<span class="success">✅ API调用成功！</span>\n\n响应数据：\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ API调用失败</span>\n\n状态码: ${response.status}\n状态文本: ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误</span>\n\n错误信息: ${error.message}\n\n可能的原因：\n1. 服务器未启动\n2. 网络连接问题\n3. CORS跨域问题`;
            }
        }

        async function testComponentsAPI() {
            const resultDiv = document.getElementById('components-result');
            resultDiv.innerHTML = '<span class="loading">正在测试组件API...</span>';
            
            try {
                const response = await fetch('http://***************:5221/api/models/components?page=1&pageSize=12', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<span class="success">✅ API调用成功！</span>\n\n响应数据：\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ API调用失败</span>\n\n状态码: ${response.status}\n状态文本: ${response.statusText}\n\n注意：组件API可能尚未实现，前端会回退到本地数据`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误</span>\n\n错误信息: ${error.message}\n\n注意：组件API可能尚未实现，前端会回退到本地数据\n\n可能的原因：\n1. 服务器未启动\n2. 网络连接问题\n3. CORS跨域问题\n4. 组件API端点不存在`;
            }
        }

        function clearResult(elementId) {
            document.getElementById(elementId).innerHTML = '结果已清除，点击测试按钮重新开始...';
        }
    </script>
</body>
</html>
