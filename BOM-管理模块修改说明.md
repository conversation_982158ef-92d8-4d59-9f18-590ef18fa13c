# 研发BOM管理模块 - 左侧型号列表功能增强

## 修改概述

在研发BOM管理模块的左侧型号列表的查询录入框下方增加了三个过滤按钮：**全部**、**成品**、**组件**，用于快速筛选不同类型的型号。

## 修改内容

### 1. 界面修改

#### 1.1 新增过滤按钮组
在搜索框下方添加了三个按钮：
- **全部**：显示所有型号
- **成品**：只显示成品类型的型号
- **组件**：只显示组件类型的型号

#### 1.2 型号显示增强
在每个型号名称旁边添加了类型标签：
- 成品：绿色标签显示"成品"
- 组件：橙色标签显示"组件"

### 2. 数据结构修改

#### 2.1 Model接口更新
```typescript
interface Model {
  id: number
  name: string
  code: string
  status: string
  type: 'finished' | 'component' // 新增类型字段
}
```

#### 2.2 示例数据更新
```typescript
const models = ref<Model[]>([
  { id: 1, name: '断路器A型', code: 'MC001', status: '启用', type: 'finished' },
  { id: 2, name: '继电器B型', code: 'MC002', status: '启用', type: 'component' },
  { id: 3, name: '接触器C型', code: 'MC003', status: '停用', type: 'component' },
  { id: 4, name: '变压器D型', code: 'MC004', status: '启用', type: 'finished' },
  { id: 5, name: '控制模块E型', code: 'MC005', status: '启用', type: 'component' },
  { id: 6, name: '配电柜F型', code: 'MC006', status: '启用', type: 'finished' },
])
```

### 3. 功能逻辑修改

#### 3.1 新增状态变量
```typescript
const modelTypeFilter = ref<'all' | 'finished' | 'component'>('all')
```

#### 3.2 过滤逻辑增强
```typescript
const filteredModels = computed(() => {
  let filteredList = models.value
  
  // 按类型过滤
  if (modelTypeFilter.value !== 'all') {
    filteredList = filteredList.filter(model => model.type === modelTypeFilter.value)
  }
  
  // 按搜索关键词过滤
  if (searchModel.value) {
    const search = searchModel.value.toLowerCase()
    filteredList = filteredList.filter(model => 
      model.name.toLowerCase().includes(search) || 
      model.code.toLowerCase().includes(search)
    )
  }
  
  return filteredList
})
```

#### 3.3 新增过滤方法
```typescript
function setModelTypeFilter(type: 'all' | 'finished' | 'component') {
  modelTypeFilter.value = type
  // 如果当前选中的型号不在过滤结果中，清除选择
  if (selectedModel.value && type !== 'all' && selectedModel.value.type !== type) {
    selectedModel.value = null
  }
}
```

### 4. 样式修改

#### 4.1 过滤按钮样式
```css
.model-filter-buttons {
  padding: 10px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  gap: 8px;
}

.filter-btn {
  flex: 1;
  font-size: 12px;
}
```

#### 4.2 型号名称样式
```css
.model-name {
  font-weight: bold;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.model-type-tag {
  font-size: 10px !important;
  height: 18px;
  line-height: 16px;
}
```

## 功能特性

### 1. 智能过滤
- 支持按类型过滤（全部/成品/组件）
- 支持按关键词搜索（型号名称或编码）
- 两种过滤方式可以同时使用

### 2. 用户体验
- 按钮采用Element Plus设计风格，与现有界面保持一致
- 激活状态的按钮有明显的视觉反馈
- 型号类型标签颜色区分，便于快速识别

### 3. 数据一致性
- 当切换过滤类型时，如果当前选中的型号不符合过滤条件，会自动清除选择
- 确保界面状态的一致性

## 文件修改清单

### 主要修改文件
- `mes-system/src/views/engineering/EngineeringBOM.vue`

### 修改行数统计
- 模板部分：新增约30行
- 脚本部分：新增约20行
- 样式部分：新增约15行

## 测试验证

创建了测试文件 `test-bom-modification.html` 来演示修改效果，包含：
- 完整的界面布局
- 交互功能演示
- 样式效果展示

## 兼容性说明

- 保持与现有代码的完全兼容
- 不影响其他功能模块
- 遵循现有的代码规范和设计模式
- 使用Element Plus组件，保持UI一致性

## 后续扩展建议

1. **数据持久化**：将过滤状态保存到本地存储，用户刷新页面后保持选择
2. **更多过滤条件**：可以考虑添加按状态过滤（启用/停用）
3. **批量操作**：在过滤结果基础上支持批量操作功能
4. **统计信息**：显示当前过滤结果的统计信息（如：共X个成品，Y个组件）

## API集成更新

### 1. 新增API调用功能

#### 1.1 成品API集成
- **API端点**: `http://***************:5221/api/models/products?page=1&pageSize=12`
- **调用时机**: 用户点击"成品"按钮时
- **数据处理**: 自动转换API响应格式为前端Model格式

#### 1.2 组件API集成
- **API端点**: `http://***************:5221/api/models/components?page=1&pageSize=12`
- **调用时机**: 用户点击"组件"按钮时
- **回退机制**: API不存在时使用本地示例数据

### 2. 数据格式适配

#### 2.1 API响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "产品名称",
        "code": "产品编码",
        "status": true,
        "specification": "规格",
        "description": "描述",
        "createTime": "2023-07-20T15:35:04.3082116",
        "updateTime": "2023-07-20T15:35:04.3082116"
      }
    ]
  }
}
```

#### 2.2 前端Model接口更新
```typescript
interface Model {
  id: number
  name: string
  code: string
  status: boolean // 更新为boolean类型
  type: 'finished' | 'component'
  specification?: string // 新增
  description?: string // 新增
  createTime?: string // 新增
  updateTime?: string // 新增
}
```

### 3. 新增API调用函数

#### 3.1 loadProductsFromAPI()
- 调用成品API获取数据
- 数据格式转换和错误处理
- 加载状态管理

#### 3.2 loadComponentsFromAPI()
- 调用组件API获取数据
- 包含回退到本地数据的机制
- 友好的错误提示

#### 3.3 loadAllLocalData()
- 重新加载本地示例数据
- 用于"全部"按钮的数据重置

### 4. 用户交互流程

1. **点击成品按钮**:
   - 显示加载状态
   - 调用成品API
   - 更新型号列表
   - 显示成功消息

2. **点击组件按钮**:
   - 显示加载状态
   - 尝试调用组件API
   - 如果失败，回退到本地数据
   - 显示相应消息

3. **点击全部按钮**:
   - 重新加载本地完整数据
   - 包含所有类型的型号

### 5. 错误处理机制

- **网络错误**: 显示网络连接提示
- **API错误**: 显示具体错误信息
- **数据格式错误**: 自动处理和转换
- **回退机制**: 组件API失败时使用本地数据

## 总结

本次修改成功在研发BOM管理模块的左侧型号列表中添加了类型过滤功能和API集成，实现了：

1. ✅ **基础过滤功能**: 全部、成品、组件三个按钮
2. ✅ **API集成**: 成品数据从后端API获取
3. ✅ **数据适配**: API响应格式自动转换
4. ✅ **错误处理**: 完善的错误处理和回退机制
5. ✅ **用户体验**: 加载状态、成功提示、错误提示
6. ✅ **向后兼容**: 保持现有功能不受影响

修改遵循了现有的设计规范，保持了良好的用户体验和代码质量，同时为后续的API扩展奠定了基础。
